"""
SQLAlchemy models for cases.

This module defines the SQLAlchemy models for cases in the PI Lawyer AI system.
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID, uuid4
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID

from .base import Base


class Case(Base):
    """SQLAlchemy model for cases."""

    __tablename__ = "cases"
    __table_args__ = {"schema": "tenants"}

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Tenant isolation
    tenant_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=False, index=True
    )

    # Basic case information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[str] = mapped_column(
        String(50), nullable=False, default="active"
    )
    case_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    sensitive: Mapped[bool] = mapped_column(Boolean, default=False)

    # Relationships
    client_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("tenants.clients.id"), nullable=True
    )
    client = relationship("Client", back_populates="cases")

    # Audit fields
    created_by: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, onupdate=datetime.utcnow
    )
    updated_by: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=True
    )

    # Additional data
    case_metadata: Mapped[dict] = mapped_column(
        JSON, nullable=False, default=dict
    )

    def __repr__(self) -> str:
        """Return string representation of the case."""
        return f"<Case(id={self.id}, title={self.title}, status={self.status})>"
