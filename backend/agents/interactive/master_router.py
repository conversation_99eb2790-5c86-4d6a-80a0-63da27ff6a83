"""
Master Router for Interactive Agents

This module provides the master router for the AiLex interactive agents system.
The master router analyzes user input and routes requests to the appropriate
specialized agent or graph based on intent detection.

The router supports routing to:
- Calendar CRUD Agent: For calendar-related operations (create, read, update, delete events)
  * Detects calendar intents using keywords and patterns
  * Routes to calendar_graph for comprehensive calendar management
  * Supports natural language calendar operations
- Task CRUD Agent: For task management operations
- Case & Client CRUD Agent: For case and client management
- Research Agent: For legal research queries
- Intake Agent: For new client intake processes

Calendar Intent Detection:
The router uses sophisticated intent detection for calendar operations, including:
- Schedule/booking keywords: "schedule", "book", "create meeting", "set appointment"
- View/read keywords: "show calendar", "view calendar", "my schedule"
- Update keywords: "reschedule", "move meeting", "change appointment"
- Delete keywords: "cancel meeting", "delete event", "remove appointment"
- Availability keywords: "when am i free", "available time", "find time"
- Explicit namespace: "calendar.create", "calendar.list", etc.

Usage:
    from backend.agents.interactive.master_router import master_router, create_master_graph

    # Use in a LangGraph StateGraph
    sg.add_node("masterRouter", master_router)

    # Or create a complete master graph
    graph = create_master_graph(voyage=voyage_client)

    # Example routing scenarios:
    # "Schedule a meeting tomorrow" → calendar_graph
    # "case.create new matter" → case_client_agent
    # "Research personal injury law" → research_agent
"""

import logging
import re
from typing import Dict, Any, Literal, TypedDict, Optional
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage

from backend.agents.interactive.calendar_crud.graph import create_calendar_graph
from shared.core.llm.voyage import VoyageClient

# Set up logging
logger = logging.getLogger(__name__)

# Define the router output type
class MasterRouterOutput(TypedDict):
    next: Literal[
        "calendar_graph", 
        "task_crud_agent", 
        "case_client_agent", 
        "research_agent", 
        "intake_agent",
        "supervisor_agent"
    ]

# Calendar intent keywords for routing
CALENDAR_KEYWORDS = [
    # Event creation
    "schedule", "book", "create meeting", "set appointment", "add event",
    "plan meeting", "arrange", "organize meeting",
    
    # Event reading/viewing
    "show calendar", "view calendar", "check calendar", "my schedule",
    "what's on my calendar", "calendar events", "upcoming meetings",
    "today's schedule", "tomorrow's schedule", "this week's schedule",
    
    # Event updating
    "reschedule", "move meeting", "change appointment", "update event",
    "modify meeting", "edit appointment",
    
    # Event deletion
    "cancel meeting", "delete event", "remove appointment", "cancel appointment",
    
    # Free/busy checking
    "when am i free", "available time", "free time", "busy time",
    "check availability", "find time", "when can we meet"
]

# Calendar intent patterns (regex)
CALENDAR_PATTERNS = [
    r"calendar\.",  # Explicit calendar namespace
    r"schedule.*(?:meeting|appointment|event)",
    r"(?:create|add|book).*(?:meeting|appointment|event)",
    r"(?:show|view|check).*calendar",
    r"(?:reschedule|move|change).*(?:meeting|appointment)",
    r"(?:cancel|delete|remove).*(?:meeting|appointment|event)",
    r"when.*(?:free|available|busy)",
    r"find.*time.*(?:meeting|meet)"
]


async def master_router(state: Dict[str, Any], config: RunnableConfig) -> MasterRouterOutput:
    """
    Master router node for interactive agents.
    
    This node analyzes user input and determines which specialized agent
    or graph should handle the request based on intent detection.
    
    Args:
        state: Agent state containing messages and context
        config: Runnable configuration
        
    Returns:
        MasterRouterOutput: Next agent/graph to execute
    """
    logger.info("Master router analyzing user input for intent detection")
    
    # Get the user input from messages
    messages = state.get("messages", [])
    if not messages:
        logger.info("No messages found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (hasattr(message, "type") and message.type == "human"):
            user_input = message.content if hasattr(message, 'content') else str(message)
            break
        elif isinstance(message, dict) and message.get("type") == "human":
            user_input = message.get("content", "")
            break
    
    if not user_input:
        logger.info("No user input found, routing to supervisor_agent")
        return {"next": "supervisor_agent"}
    
    logger.info(f"Analyzing user input: {user_input[:100]}...")
    
    # Check for calendar intent
    if _is_calendar_intent(user_input):
        logger.info("Calendar intent detected, routing to calendar_graph")
        return {"next": "calendar_graph"}
    
    # Check for explicit agent routing (e.g., "case.create", "task.list")
    if user_input.lower().startswith("case.") or user_input.lower().startswith("client."):
        logger.info("Case/Client intent detected, routing to case_client_agent")
        return {"next": "case_client_agent"}
    
    if user_input.lower().startswith("task."):
        logger.info("Task intent detected, routing to task_crud_agent")
        return {"next": "task_crud_agent"}
    
    # Check for research keywords
    research_keywords = ["research", "find", "search", "lookup", "legal", "case law", "statute"]
    if any(keyword in user_input.lower() for keyword in research_keywords):
        logger.info("Research intent detected, routing to research_agent")
        return {"next": "research_agent"}
    
    # Check for intake keywords (for new matters)
    intake_keywords = ["new client", "intake", "new case", "new matter", "client information"]
    if any(keyword in user_input.lower() for keyword in intake_keywords):
        logger.info("Intake intent detected, routing to intake_agent")
        return {"next": "intake_agent"}
    
    # Default to supervisor agent for complex routing decisions
    logger.info("No specific intent detected, routing to supervisor_agent")
    return {"next": "supervisor_agent"}


def _is_calendar_intent(user_input: str) -> bool:
    """
    Determine if the user input indicates calendar-related intent.
    
    Args:
        user_input: The user's input text
        
    Returns:
        bool: True if calendar intent is detected, False otherwise
    """
    user_input_lower = user_input.lower()
    
    # Check for explicit calendar keywords
    for keyword in CALENDAR_KEYWORDS:
        if keyword in user_input_lower:
            logger.debug(f"Calendar keyword '{keyword}' found in user input")
            return True
    
    # Check for calendar patterns using regex
    for pattern in CALENDAR_PATTERNS:
        if re.search(pattern, user_input_lower):
            logger.debug(f"Calendar pattern '{pattern}' matched in user input")
            return True
    
    return False


def create_master_graph(*, voyage: VoyageClient) -> Any:
    """
    Create the master graph that routes to specialized agents and graphs.

    This function builds a LangGraph StateGraph that implements the master routing
    workflow, connecting the master router to various specialized agents including
    the calendar graph.

    Args:
        voyage: VoyageClient instance for LLM operations

    Returns:
        StateGraph: Compiled master routing workflow graph
    """
    from langgraph.graph import StateGraph, END

    logger.info("Creating master routing StateGraph workflow")

    # Create the graph with Dict[str, Any] as the state type
    workflow = StateGraph(Dict[str, Any])

    # Add the master router node
    workflow.add_node("master_router", master_router)

    # Create and add the calendar graph
    calendar_graph = create_calendar_graph(voyage=voyage)
    workflow.add_node("calendar_graph", calendar_graph)

    # Add placeholder nodes for other agents (to be implemented)
    # These would be replaced with actual agent implementations
    async def placeholder_agent(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """Placeholder agent implementation."""
        agent_name = state.get("_current_agent", "unknown")
        logger.info(f"Placeholder agent '{agent_name}' executed")
        state["messages"] = state.get("messages", []) + [{
            "type": "assistant",
            "content": f"This is a placeholder response from {agent_name}. Implementation pending."
        }]
        return state

    workflow.add_node("task_crud_agent", placeholder_agent)
    workflow.add_node("case_client_agent", placeholder_agent)
    workflow.add_node("research_agent", placeholder_agent)
    workflow.add_node("intake_agent", placeholder_agent)
    workflow.add_node("supervisor_agent", placeholder_agent)

    # Set the entry point
    workflow.set_entry_point("master_router")

    # Add conditional edges from master router to specialized agents
    workflow.add_conditional_edges(
        "master_router",
        lambda state: state.get("next", "supervisor_agent"),
        {
            "calendar_graph": "calendar_graph",
            "task_crud_agent": "task_crud_agent",
            "case_client_agent": "case_client_agent",
            "research_agent": "research_agent",
            "intake_agent": "intake_agent",
            "supervisor_agent": "supervisor_agent",
        }
    )

    # Add edges from all agents to END
    workflow.add_edge("calendar_graph", END)
    workflow.add_edge("task_crud_agent", END)
    workflow.add_edge("case_client_agent", END)
    workflow.add_edge("research_agent", END)
    workflow.add_edge("intake_agent", END)
    workflow.add_edge("supervisor_agent", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()

    logger.info("Master routing StateGraph workflow created and compiled successfully")
    return compiled_workflow


def get_workflow_info() -> Dict[str, Any]:
    """
    Get information about the master routing workflow.

    Returns:
        Dict[str, Any]: Workflow information including nodes, edges, and capabilities
    """
    return {
        "name": "master_routing_workflow",
        "description": "LangGraph workflow for routing to specialized agents",
        "version": "1.0.0",
        "nodes": [
            "master_router",
            "calendar_graph",
            "task_crud_agent",
            "case_client_agent",
            "research_agent",
            "intake_agent",
            "supervisor_agent"
        ],
        "entry_point": "master_router",
        "capabilities": [
            "Intent detection and routing",
            "Calendar operations routing",
            "Task management routing",
            "Case and client management routing",
            "Legal research routing",
            "Client intake routing",
            "Fallback to supervisor agent"
        ],
        "supported_intents": [
            "calendar.*",
            "task.*",
            "case.*",
            "client.*",
            "research queries",
            "intake processes"
        ]
    }
